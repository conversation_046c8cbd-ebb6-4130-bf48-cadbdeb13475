# 管理员后台 - 用户管理功能说明

## 功能概述

用户管理模块是管理员后台的核心功能之一，提供了完整的用户管理解决方案，包括：

- 用户列表查询与筛选
- 用户详细信息查看
- 用户状态管理
- 批量操作
- 用户统计分析
- 数据导出功能

## 文件结构

```
app/admin/controller/UserController.php  # 用户管理控制器
app/model/User.php                       # 用户模型（已增强）
config/route.php                         # 路由配置（已添加管理员路由）
docs/admin_user_api.md                   # API接口文档
docs/admin_user_management.md            # 功能说明文档
tests/UserControllerTest.php             # 测试脚本
```

## 主要功能

### 1. 用户列表管理

**支持的查询条件：**
- 昵称模糊查询
- 用户ID精确查询
- 用户状态筛选（正常/禁用）
- 注册时间范围查询（开始时间-结束时间）

**列表特性：**
- 按用户ID倒序排列
- 分页显示
- 显示用户基本信息和余额信息
- 显示微信绑定状态

### 2. 用户详情查看

**详情信息包括：**
- 用户基本信息
- 账户余额详情
- 第三方账号绑定信息
- 最近竞猜记录
- 最近提现记录
- 邀请关系信息

### 3. 用户状态管理

**支持操作：**
- 单个用户状态切换（启用/禁用）
- 批量用户状态操作
- 用户信息编辑

### 4. 统计分析

**统计指标：**
- 总用户数
- 正常/禁用用户数
- 今日新增用户
- 本月新增用户
- 用户余额统计（总余额、冻结金额、累计收入等）

### 5. 数据导出

**导出功能：**
- 支持按筛选条件导出
- CSV格式导出
- 包含用户基本信息和余额信息

## 使用方法

### 1. 启动项目

确保项目已正确配置数据库连接，然后启动Webman服务：

```bash
php start.php start
```

### 2. 访问管理员接口

所有管理员接口都需要通过 `AdminAuth` 中间件进行身份验证。

**基础URL：** `http://your-domain.com/admin/user/`

### 3. 常用接口示例

**获取用户列表：**
```bash
GET /admin/user/list?page=1&limit=20&nickname=张三&status=1
```

**查看用户详情：**
```bash
GET /admin/user/show?id=100001
```

**更新用户状态：**
```bash
POST /admin/user/update-status
Content-Type: application/json

{
    "id": 100001,
    "status": 0
}
```

**批量操作：**
```bash
POST /admin/user/batch-action
Content-Type: application/json

{
    "action": "disable",
    "ids": [100001, 100002, 100003]
}
```

## 数据模型增强

### User模型新增功能

1. **状态常量定义：**
   - `STATUS_DISABLED = 0` (禁用)
   - `STATUS_NORMAL = 1` (正常)

2. **性别常量定义：**
   - `GENDER_UNKNOWN = 0` (未知)
   - `GENDER_MALE = 1` (男)
   - `GENDER_FEMALE = 2` (女)

3. **新增方法：**
   - `getStatusList()` - 获取状态列表
   - `getGenderList()` - 获取性别列表
   - `getStatusNameAttribute()` - 获取状态名称
   - `getGenderNameAttribute()` - 获取性别名称

## 权限控制

用户管理功能通过 `AdminAuth` 中间件进行权限控制：

1. **身份验证：** 检查管理员登录状态
2. **角色验证：** 确保用户具有管理员权限
3. **状态检查：** 确保管理员账号状态正常

## 测试

运行测试脚本验证功能：

```bash
php tests/UserControllerTest.php
```

测试内容包括：
- 数据库连接测试
- 用户模型常量测试
- 筛选选项测试
- 用户统计测试
- 用户列表查询测试

## 注意事项

1. **数据安全：** 所有用户敏感信息都经过适当处理
2. **性能优化：** 使用了模型关联预加载减少数据库查询
3. **错误处理：** 完善的异常捕获和错误信息返回
4. **参数验证：** 严格的输入参数验证和过滤

## 扩展建议

1. **日志记录：** 可以添加管理员操作日志记录
2. **权限细化：** 可以根据管理员角色细化操作权限
3. **数据缓存：** 对统计数据可以添加缓存机制
4. **导出优化：** 大量数据导出可以使用队列异步处理

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `config/think-orm.php` 配置
   - 确认数据库服务正常运行

2. **权限验证失败**
   - 检查 `AdminAuth` 中间件配置
   - 确认管理员账号状态正常

3. **模型关联查询失败**
   - 检查模型关联关系定义
   - 确认相关数据表存在

### 调试方法

1. 启用调试模式查看详细错误信息
2. 检查日志文件获取错误详情
3. 使用测试脚本验证各个功能模块

## 更新日志

- **v1.0.0** - 初始版本，包含基础用户管理功能
- 支持用户列表查询、详情查看、状态管理
- 支持批量操作和数据导出
- 完善的权限控制和错误处理
