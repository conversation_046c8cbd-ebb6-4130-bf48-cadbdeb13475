<?php

return [
    'default' => 'pgsql',
    'connections' => [
        'mysql' => [
            // 数据库类型
            'type' => 'mysql',
            // 服务器地址
            'hostname' => getenv("DB_HOST"),
            // 数据库名
            'database' => getenv("DB_DATABASE"),
            // 数据库用户名
            'username' => getenv("DB_USERNAME"),
            // 数据库密码
            'password' => getenv("DB_PASSWORD"),
            // 数据库连接端口
            'hostport' => getenv("DB_PORT"),
            // 数据库连接参数
            'params' => [
                // 连接超时3秒
                \PDO::ATTR_TIMEOUT => 3,
            ],
            // 数据库编码默认采用utf8
            'charset' => getenv("DB_CHARSET"),
            // 数据库表前缀
            'prefix' => '',
            // 断线重连
            'break_reconnect' => true,
            // 自定义分页类
            'bootstrap' =>  '',
            // 连接池配置
            'pool' => [
                'max_connections' => 5, // 最大连接数
                'min_connections' => 1, // 最小连接数
                'wait_timeout' => 3,    // 从连接池获取连接等待超时时间
                'idle_timeout' => 60,   // 连接最大空闲时间，超过该时间会被回收
                'heartbeat_interval' => 50, // 心跳检测间隔，需要小于60秒
            ],
        ],
        'pgsql' => [
            // 数据库类型
            'type' => 'pgsql',
            // 服务器地址
            'hostname' => getenv("DB_HOST") ?: '127.0.0.1',
            // 数据库名
            'database' => getenv("DB_DATABASE") ?: 'minisoft_api',
            // 数据库用户名
            'username' => getenv("DB_USERNAME") ?: 'postgres',
            // 数据库密码
            'password' => getenv("DB_PASSWORD") ?: '',
            // 数据库连接端口
            'hostport' => getenv("DB_PORT") ?: 5432,
            // 数据库连接参数
            'params' => [
                // 连接超时3秒
                \PDO::ATTR_TIMEOUT => 3,
            ],
            // 数据库编码默认采用utf8
            'charset' => getenv("DB_CHARSET") ?: 'utf8',
            // 数据库表前缀
            'prefix' => '',
            // 断线重连
            'break_reconnect' => true,
            // 自定义分页类
            'bootstrap' =>  '',
            // 连接池配置
            'pool' => [
                'max_connections' => 5, // 最大连接数
                'min_connections' => 1, // 最小连接数
                'wait_timeout' => 3,    // 从连接池获取连接等待超时时间
                'idle_timeout' => 60,   // 连接最大空闲时间，超过该时间会被回收
                'heartbeat_interval' => 50, // 心跳检测间隔，需要小于60秒
            ],
        ],
    ],
];
