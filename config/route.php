<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use support\Request;
use Webman\Route;
use Workerman\Coroutine;
use Workerman\Timer;



Route::group('/api',function (){

    Route::any('/index',[app\api\controller\IndexController::class,'index']);
    Route::any('/getOpenData',[app\api\controller\IndexController::class,'getOpenData']);

})->middleware([
    app\middleware\ApiAuth::class,
]);

// 管理员后台路由
Route::group('/admin', function () {

    // 用户管理
    Route::group('/user', function () {
        Route::get('/list', [app\admin\controller\UserController::class, 'index']);
        Route::get('/show', [app\admin\controller\UserController::class, 'show']);
        Route::post('/update', [app\admin\controller\UserController::class, 'update']);
        Route::post('/update-status', [app\admin\controller\UserController::class, 'updateStatus']);
        Route::post('/batch-action', [app\admin\controller\UserController::class, 'batchAction']);
        Route::get('/statistics', [app\admin\controller\UserController::class, 'statistics']);
        Route::get('/filter-options', [app\admin\controller\UserController::class, 'getFilterOptions']);
        Route::get('/export', [app\admin\controller\UserController::class, 'export']);
        Route::post('/reset-password', [app\admin\controller\UserController::class, 'resetPassword']);
    });

})->middleware([
    app\middleware\AdminAuth::class,
]);

Route::any('/', function (){
    return "123";
});



