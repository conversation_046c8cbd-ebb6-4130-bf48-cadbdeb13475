# 数据库修改记录

## 修改日期
2025-07-04

## 修改内容

### 1. 用户ID自增起始值修改
- **修改内容**: 用户表ID从100000开始自增
- **实现方式**: 
  ```sql
  CREATE SEQUENCE users_id_seq START 100000;
  CREATE TABLE users (
      id BIGINT PRIMARY KEY DEFAULT nextval('users_id_seq'),
      ...
  );
  ALTER SEQUENCE users_id_seq OWNED BY users.id;
  ```

### 2. 金币系统改为现金系统
- **修改范围**: 所有涉及金币的表、字段、模型和配置
- **主要变更**:

#### 表结构变更
1. **用户资金表 (user_balances)**
   - `coin_balance` → `cash_balance` (现金余额，单位：元)
   - `frozen_coins` → `frozen_cash` (冻结现金，单位：元)
   - 其他金额字段注释更新为现金单位

2. **金币记录表重命名**
   - `coin_records` → `cash_records`
   - 表注释和字段注释更新为现金相关

3. **提现记录表 (withdrawal_records)**
   - 移除 `coin_amount` 字段（消耗金币数量）
   - 移除 `exchange_rate` 字段（兑换比例）
   - 直接使用现金金额进行提现

4. **竞猜相关表**
   - `guess_records`: `bet_amount` 和 `reward_amount` 注释更新为现金单位
   - `guess_rules`: `bet_amount` 和 `reward_amount` 注释更新为现金单位
   - `invitation_records`: `reward_amount` 注释更新为现金单位

#### 模型文件变更
1. **UserBalance.php**
   - 更新 `fillable` 字段名称
   - 更新 `casts` 字段名称
   - 方法名称更新：`addCoins()` → `addCash()`
   - 方法名称更新：`reduceCoins()` → `reduceCash()`
   - 方法名称更新：`freezeCoins()` → `freezeCash()`
   - 方法名称更新：`unfreezeCoins()` → `unfreezeCash()`

2. **CoinRecord.php → CashRecord.php**
   - 文件重命名
   - 类名更新：`CoinRecord` → `CashRecord`
   - 表名更新：`coin_records` → `cash_records`
   - 注释更新为现金相关

3. **WithdrawalRecord.php**
   - 移除 `coin_amount` 和 `exchange_rate` 字段
   - 更新 `fillable` 和 `casts` 配置

4. **User.php**
   - 关联关系更新：`coinRecords()` → `cashRecords()`

#### 系统配置变更
- 移除金币兑换现金比例配置
- 邀请奖励配置更新为现金单位（元）
- 新用户注册奖励更新为现金单位（元）

#### 初始数据变更
- 竞猜规则数据：投注和奖励金额保持不变，但单位从金币改为元
- 系统配置：移除金币相关配置，保留现金相关配置

### 3. PostgreSQL兼容性优化
- 移除所有 `COMMENT` 语法，改为行内注释
- 确保所有数据类型和语法符合PostgreSQL规范

## 影响范围
1. **数据库表结构**: 11个表全部更新
2. **模型文件**: 6个模型文件更新
3. **业务逻辑**: 所有涉及金币的业务逻辑需要相应调整
4. **前端显示**: 所有金币相关的显示需要更新为现金

## 注意事项
1. 现有的业务逻辑代码需要相应更新
2. 前端页面的金币显示需要更新为现金显示
3. API接口的字段名称可能需要调整
4. 测试用例需要相应更新

### 4. 新增资金流水记录表
- **新增表**: `fund_flow_records`
- **目的**: 提供更详细的资金变动追踪和审计功能
- **主要特性**:
  - 唯一流水号生成
  - 详细的来源和渠道记录
  - IP地址和设备信息记录
  - 操作员记录（管理员操作时）
  - 扩展数据支持（JSON格式）
  - 完整的状态管理

## 影响范围
1. **数据库表结构**: 12个表（新增1个资金流水记录表）
2. **模型文件**: 7个模型文件（新增FundFlowRecord.php）
3. **业务逻辑**: 所有涉及金币的业务逻辑需要相应调整
4. **前端显示**: 所有金币相关的显示需要更新为现金

## 后续工作
1. 更新控制器和服务类中的相关方法
2. 集成资金流水记录到业务流程中
3. 更新API文档
4. 更新前端页面显示
5. 编写和执行测试用例
