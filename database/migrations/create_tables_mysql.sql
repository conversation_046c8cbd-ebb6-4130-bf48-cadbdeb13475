-- 微信小程序有奖竞猜数据库表结构
-- 数据库: MySQL 8.0+
-- 创建时间: 2025-07-04

-- 1. 用户表
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    nickname VARCHAR(100) NOT NULL DEFAULT '' COMMENT '昵称',
    avatar VARCHAR(500) NOT NULL DEFAULT '' COMMENT '头像',
    phone VARCHAR(20) NOT NULL DEFAULT '' COMMENT '手机号',
    email VARCHAR(100) NOT NULL DEFAULT '' COMMENT '邮箱',
    gender TINYINT NOT NULL DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    birthday DATE NULL COMMENT '生日',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) NOT NULL DEFAULT '' COMMENT '最后登录IP',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 设置用户ID自增起始值为100000
ALTER TABLE users AUTO_INCREMENT = 100000;

-- 2. 第三方账号表
CREATE TABLE third_party_accounts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    platform VARCHAR(20) NOT NULL COMMENT '平台：wechat-微信',
    platform_user_id VARCHAR(100) NOT NULL COMMENT '第三方平台用户ID',
    openid VARCHAR(100) NOT NULL DEFAULT '' COMMENT '微信openid',
    unionid VARCHAR(100) NOT NULL DEFAULT '' COMMENT '微信unionid',
    session_key VARCHAR(100) NOT NULL DEFAULT '' COMMENT '会话密钥',
    access_token TEXT NOT NULL COMMENT '访问令牌',
    refresh_token TEXT NOT NULL COMMENT '刷新令牌',
    expires_in INT NOT NULL DEFAULT 0 COMMENT '过期时间',
    extra_data JSON NULL COMMENT '额外数据',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_platform_user (platform, platform_user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='第三方账号表';

-- 3. 用户资金表
CREATE TABLE user_balances (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL UNIQUE COMMENT '用户ID',
    cash_balance DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '现金余额（元）',
    frozen_cash DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '冻结现金（元）',
    total_earned DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '累计获得现金（元）',
    total_spent DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '累计消费现金（元）',
    total_withdrawn DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '累计提现金额（元）',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户资金表';

-- 4. 开奖结果表
CREATE TABLE lottery_results (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    lottery_date DATE NOT NULL UNIQUE COMMENT '开奖日期',
    shanghai_index DECIMAL(10,2) NOT NULL COMMENT '上证指数',
    shenzhen_index DECIMAL(10,2) NOT NULL COMMENT '深证成指',
    chinext_index DECIMAL(10,2) NOT NULL COMMENT '创业板指',
    winning_number VARCHAR(4) NOT NULL COMMENT '中奖号码（四位尾数）',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-未开奖，1-已开奖',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='开奖结果表';

-- 5. 竞猜记录表
CREATE TABLE guess_records (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    lottery_date DATE NOT NULL COMMENT '竞猜期数（日期）',
    guess_number VARCHAR(4) NOT NULL COMMENT '竞猜号码',
    bet_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '投注现金数量（元）',
    is_winner TINYINT NOT NULL DEFAULT 0 COMMENT '是否中奖：0-未中奖，1-中奖',
    reward_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '奖励现金数量（元）',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-待开奖，1-已开奖',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    KEY idx_lottery_date (lottery_date),
    KEY idx_user_lottery (user_id, lottery_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='竞猜记录表';

-- 6. 现金记录表
CREATE TABLE cash_records (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    type TINYINT NOT NULL COMMENT '类型：1-竞猜投注，2-中奖奖励，3-邀请奖励，4-提现扣除，5-系统赠送，6-系统扣除',
    amount DECIMAL(15,2) NOT NULL COMMENT '现金数量（正数为增加，负数为减少）',
    balance_before DECIMAL(15,2) NOT NULL COMMENT '变动前余额',
    balance_after DECIMAL(15,2) NOT NULL COMMENT '变动后余额',
    related_id BIGINT UNSIGNED NULL COMMENT '关联ID（如竞猜记录ID、邀请记录ID等）',
    remark VARCHAR(500) NOT NULL DEFAULT '' COMMENT '备注',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    KEY idx_user_id (user_id),
    KEY idx_type (type),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='现金记录表';

-- 7. 提现记录表
CREATE TABLE withdrawal_records (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    amount DECIMAL(10,2) NOT NULL COMMENT '提现金额（元）',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-申请中，1-处理中，2-已完成，3-已拒绝',
    platform VARCHAR(20) NOT NULL DEFAULT 'wechat' COMMENT '提现平台',
    platform_account VARCHAR(100) NOT NULL DEFAULT '' COMMENT '提现账号',
    transaction_id VARCHAR(100) NOT NULL DEFAULT '' COMMENT '交易流水号',
    reject_reason VARCHAR(500) NOT NULL DEFAULT '' COMMENT '拒绝原因',
    processed_at TIMESTAMP NULL COMMENT '处理时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    KEY idx_user_id (user_id),
    KEY idx_status (status),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提现记录表';

-- 8. 邀请记录表
CREATE TABLE invitation_records (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    inviter_id BIGINT UNSIGNED NOT NULL COMMENT '邀请人ID',
    invitee_id BIGINT UNSIGNED NOT NULL COMMENT '被邀请人ID',
    level TINYINT NOT NULL DEFAULT 1 COMMENT '邀请层级：1-直接邀请，2-间接邀请',
    reward_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '邀请奖励现金（元）',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-无效，1-有效',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (inviter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (invitee_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_inviter_invitee (inviter_id, invitee_id),
    KEY idx_inviter_id (inviter_id),
    KEY idx_invitee_id (invitee_id),
    KEY idx_level (level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邀请记录表';

-- 9. 竞猜规则表
CREATE TABLE guess_rules (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '规则名称',
    bet_amount DECIMAL(10,2) NOT NULL COMMENT '投注现金数量（元）',
    reward_amount DECIMAL(10,2) NOT NULL COMMENT '中奖奖励现金数量（元）',
    is_active TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    KEY idx_is_active (is_active),
    KEY idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='竞猜规则表';

-- 10. 系统配置表
CREATE TABLE system_configs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_desc VARCHAR(500) NOT NULL DEFAULT '' COMMENT '配置描述',
    config_type VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string,int,float,bool,json',
    is_public TINYINT NOT NULL DEFAULT 0 COMMENT '是否公开（前端可获取）：0-否，1-是',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    KEY idx_config_key (config_key),
    KEY idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 11. 资金流水记录表
CREATE TABLE fund_flow_records (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    flow_no VARCHAR(32) NOT NULL UNIQUE COMMENT '流水号（唯一）',
    type TINYINT NOT NULL COMMENT '类型：1-竞猜投注，2-中奖奖励，3-邀请奖励，4-提现扣除，5-系统赠送，6-系统扣除，7-充值，8-退款',
    sub_type TINYINT NOT NULL DEFAULT 0 COMMENT '子类型：具体业务类型',
    amount DECIMAL(15,2) NOT NULL COMMENT '变动金额（正数为收入，负数为支出）',
    balance_before DECIMAL(15,2) NOT NULL COMMENT '变动前余额',
    balance_after DECIMAL(15,2) NOT NULL COMMENT '变动后余额',
    source_type VARCHAR(50) NOT NULL DEFAULT '' COMMENT '来源类型：guess_win,invitation,system_gift,withdrawal等',
    source_id BIGINT UNSIGNED NULL COMMENT '来源记录ID',
    channel VARCHAR(50) NOT NULL DEFAULT 'system' COMMENT '渠道：system,wechat,admin等',
    operator_id BIGINT UNSIGNED NULL COMMENT '操作员ID（管理员操作时）',
    client_ip VARCHAR(45) NOT NULL DEFAULT '' COMMENT '客户端IP',
    user_agent VARCHAR(500) NOT NULL DEFAULT '' COMMENT '用户代理',
    device_info JSON NULL COMMENT '设备信息',
    extra_data JSON NULL COMMENT '扩展数据',
    remark VARCHAR(1000) NOT NULL DEFAULT '' COMMENT '备注',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-失败，1-成功，2-处理中',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    KEY idx_user_id (user_id),
    KEY idx_flow_no (flow_no),
    KEY idx_type (type),
    KEY idx_source_type (source_type),
    KEY idx_source_id (source_id),
    KEY idx_status (status),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资金流水记录表';

-- 12. 公告表
CREATE TABLE announcements (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '公告标题',
    content TEXT NOT NULL COMMENT '公告内容',
    type TINYINT NOT NULL DEFAULT 1 COMMENT '类型：1-系统公告，2-活动公告，3-维护公告',
    is_popup TINYINT NOT NULL DEFAULT 0 COMMENT '是否弹窗显示：0-否，1-是',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    KEY idx_type (type),
    KEY idx_status (status),
    KEY idx_start_time (start_time),
    KEY idx_end_time (end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公告表';

-- 插入初始系统配置数据
INSERT INTO system_configs (config_key, config_value, config_desc, config_type, is_public) VALUES
('min_withdrawal_amount', '10', '最小提现金额（元）', 'int', 1),
('max_withdrawal_amount', '1000', '最大提现金额（元）', 'int', 1),
('daily_guess_limit', '10', '每日竞猜次数限制', 'int', 1),
('invitation_reward_level1', '10', '一级邀请奖励现金（元）', 'int', 0),
('invitation_reward_level2', '5', '二级邀请奖励现金（元）', 'int', 0),
('lottery_time', '12:00:00', '每日开奖时间', 'string', 1),
('guess_deadline', '11:59:59', '竞猜截止时间', 'string', 1),
('new_user_bonus', '100', '新用户注册奖励现金（元）', 'int', 0),
('app_name', '有奖竞猜', '应用名称', 'string', 1),
('app_version', '1.0.0', '应用版本', 'string', 1);

-- 13. 管理员表
CREATE TABLE admins (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    real_name VARCHAR(100) NOT NULL DEFAULT '' COMMENT '真实姓名',
    email VARCHAR(100) NOT NULL DEFAULT '' COMMENT '邮箱',
    phone VARCHAR(20) NOT NULL DEFAULT '' COMMENT '手机号',
    avatar VARCHAR(500) NOT NULL DEFAULT '' COMMENT '头像',
    role VARCHAR(20) NOT NULL DEFAULT 'admin' COMMENT '角色：super_admin-超级管理员，admin-管理员，editor-编辑员',
    permissions JSON NULL COMMENT '权限列表',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) NOT NULL DEFAULT '' COMMENT '最后登录IP',
    login_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '登录次数',
    remark VARCHAR(500) NOT NULL DEFAULT '' COMMENT '备注',
    created_by BIGINT UNSIGNED NULL COMMENT '创建人ID',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    KEY idx_username (username),
    KEY idx_role (role),
    KEY idx_status (status),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 插入初始管理员数据
INSERT INTO admins (username, password, real_name, email, role, status, remark) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '超级管理员', '<EMAIL>', 'super_admin', 1, '系统默认超级管理员');

-- 插入初始竞猜规则数据
INSERT INTO guess_rules (name, bet_amount, reward_amount, is_active, sort_order) VALUES
('标准竞猜', 10.00, 100.00, 1, 1),
('高级竞猜', 50.00, 600.00, 1, 2),
('豪华竞猜', 100.00, 1500.00, 1, 3);
