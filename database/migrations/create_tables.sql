-- 微信小程序有奖竞猜数据库表结构
-- 数据库: PostgreSQL
-- 创建时间: 2025-07-04

-- 1. 用户表
CREATE SEQUENCE users_id_seq START 100000;
CREATE TABLE users (
    id BIGINT PRIMARY KEY DEFAULT nextval('users_id_seq'),
    nickname VARCHAR(100) NOT NULL DEFAULT '',
    avatar VARCHAR(500) NOT NULL DEFAULT '',
    phone VARCHAR(20) NOT NULL DEFAULT '',
    email VARCHAR(100) NOT NULL DEFAULT '',
    gender SMALLINT NOT NULL DEFAULT 0, -- 性别：0-未知，1-男，2-女
    birthday DATE NULL,
    status SMALLINT NOT NULL DEFAULT 1, -- 状态：0-禁用，1-正常
    last_login_time TIMESTAMP NULL,
    last_login_ip VARCHAR(45) NOT NULL DEFAULT '',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
ALTER SEQUENCE users_id_seq OWNED BY users.id;

-- 2. 第三方账号表
CREATE TABLE third_party_accounts (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    platform VARCHAR(20) NOT NULL, -- 平台：wechat-微信
    platform_user_id VARCHAR(100) NOT NULL, -- 第三方平台用户ID
    openid VARCHAR(100) NOT NULL DEFAULT '',
    unionid VARCHAR(100) NOT NULL DEFAULT '',
    session_key VARCHAR(100) NOT NULL DEFAULT '',
    access_token TEXT NOT NULL DEFAULT '',
    refresh_token TEXT NOT NULL DEFAULT '',
    expires_in INTEGER NOT NULL DEFAULT 0,
    extra_data JSONB NULL, -- 额外数据
    status SMALLINT NOT NULL DEFAULT 1, -- 状态：0-禁用，1-正常
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 3. 用户资金表
CREATE TABLE user_balances (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    cash_balance DECIMAL(15,2) NOT NULL DEFAULT 0.00, -- 现金余额（元）
    frozen_cash DECIMAL(15,2) NOT NULL DEFAULT 0.00, -- 冻结现金（元）
    total_earned DECIMAL(15,2) NOT NULL DEFAULT 0.00, -- 累计获得现金（元）
    total_spent DECIMAL(15,2) NOT NULL DEFAULT 0.00, -- 累计消费现金（元）
    total_withdrawn DECIMAL(15,2) NOT NULL DEFAULT 0.00, -- 累计提现金额（元）
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 4. 开奖结果表
CREATE TABLE lottery_results (
    id BIGSERIAL PRIMARY KEY,
    lottery_date DATE NOT NULL UNIQUE, -- 开奖日期
    shanghai_index DECIMAL(10,2) NOT NULL, -- 上证指数
    shenzhen_index DECIMAL(10,2) NOT NULL, -- 深证成指
    chinext_index DECIMAL(10,2) NOT NULL, -- 创业板指
    winning_number VARCHAR(4) NOT NULL, -- 中奖号码（四位尾数）
    status SMALLINT NOT NULL DEFAULT 0, -- 状态：0-未开奖，1-已开奖
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 5. 竞猜记录表
CREATE TABLE guess_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    lottery_date DATE NOT NULL, -- 竞猜期数（日期）
    guess_number VARCHAR(4) NOT NULL, -- 竞猜号码
    bet_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00, -- 投注现金数量（元）
    is_winner BOOLEAN NOT NULL DEFAULT FALSE, -- 是否中奖
    reward_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00, -- 奖励现金数量（元）
    status SMALLINT NOT NULL DEFAULT 0, -- 状态：0-待开奖，1-已开奖
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 6. 现金记录表
CREATE TABLE cash_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    type SMALLINT NOT NULL, -- 类型：1-竞猜投注，2-中奖奖励，3-邀请奖励，4-提现扣除，5-系统赠送，6-系统扣除
    amount DECIMAL(15,2) NOT NULL, -- 现金数量（正数为增加，负数为减少）
    balance_before DECIMAL(15,2) NOT NULL, -- 变动前余额
    balance_after DECIMAL(15,2) NOT NULL, -- 变动后余额
    related_id BIGINT NULL, -- 关联ID（如竞猜记录ID、邀请记录ID等）
    remark VARCHAR(500) NOT NULL DEFAULT '', -- 备注
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 7. 提现记录表
CREATE TABLE withdrawal_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    amount DECIMAL(10,2) NOT NULL, -- 提现金额（元）
    status SMALLINT NOT NULL DEFAULT 0, -- 状态：0-申请中，1-处理中，2-已完成，3-已拒绝
    platform VARCHAR(20) NOT NULL DEFAULT 'wechat', -- 提现平台
    platform_account VARCHAR(100) NOT NULL DEFAULT '', -- 提现账号
    transaction_id VARCHAR(100) NOT NULL DEFAULT '', -- 交易流水号
    reject_reason VARCHAR(500) NOT NULL DEFAULT '', -- 拒绝原因
    processed_at TIMESTAMP NULL, -- 处理时间
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 8. 邀请记录表
CREATE TABLE invitation_records (
    id BIGSERIAL PRIMARY KEY,
    inviter_id BIGINT NOT NULL, -- 邀请人ID
    invitee_id BIGINT NOT NULL, -- 被邀请人ID
    level SMALLINT NOT NULL DEFAULT 1, -- 邀请层级：1-直接邀请，2-间接邀请
    reward_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00, -- 邀请奖励现金（元）
    status SMALLINT NOT NULL DEFAULT 1, -- 状态：0-无效，1-有效
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inviter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (invitee_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 9. 竞猜规则表
CREATE TABLE guess_rules (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL, -- 规则名称
    bet_amount DECIMAL(10,2) NOT NULL, -- 投注现金数量（元）
    reward_amount DECIMAL(10,2) NOT NULL, -- 中奖奖励现金数量（元）
    is_active BOOLEAN NOT NULL DEFAULT TRUE, -- 是否启用
    sort_order INTEGER NOT NULL DEFAULT 0, -- 排序
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 10. 系统配置表
CREATE TABLE system_configs (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE, -- 配置键
    config_value TEXT NOT NULL DEFAULT '', -- 配置值
    config_desc VARCHAR(500) NOT NULL DEFAULT '', -- 配置描述
    config_type VARCHAR(20) NOT NULL DEFAULT 'string', -- 配置类型：string,int,float,bool,json
    is_public BOOLEAN NOT NULL DEFAULT FALSE, -- 是否公开（前端可获取）
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 11. 资金流水记录表
CREATE TABLE fund_flow_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    flow_no VARCHAR(32) NOT NULL UNIQUE, -- 流水号（唯一）
    type SMALLINT NOT NULL, -- 类型：1-竞猜投注，2-中奖奖励，3-邀请奖励，4-提现扣除，5-系统赠送，6-系统扣除，7-充值，8-退款
    sub_type SMALLINT NOT NULL DEFAULT 0, -- 子类型：具体业务类型
    amount DECIMAL(15,2) NOT NULL, -- 变动金额（正数为收入，负数为支出）
    balance_before DECIMAL(15,2) NOT NULL, -- 变动前余额
    balance_after DECIMAL(15,2) NOT NULL, -- 变动后余额
    source_type VARCHAR(50) NOT NULL DEFAULT '', -- 来源类型：guess_win,invitation,system_gift,withdrawal等
    source_id BIGINT NULL, -- 来源记录ID
    channel VARCHAR(50) NOT NULL DEFAULT 'system', -- 渠道：system,wechat,admin等
    operator_id BIGINT NULL, -- 操作员ID（管理员操作时）
    client_ip VARCHAR(45) NOT NULL DEFAULT '', -- 客户端IP
    user_agent VARCHAR(500) NOT NULL DEFAULT '', -- 用户代理
    device_info JSONB NULL, -- 设备信息
    extra_data JSONB NULL, -- 扩展数据
    remark VARCHAR(1000) NOT NULL DEFAULT '', -- 备注
    status SMALLINT NOT NULL DEFAULT 1, -- 状态：0-失败，1-成功，2-处理中
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 12. 公告表
CREATE TABLE announcements (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL, -- 公告标题
    content TEXT NOT NULL, -- 公告内容
    type SMALLINT NOT NULL DEFAULT 1, -- 类型：1-系统公告，2-活动公告，3-维护公告
    is_popup BOOLEAN NOT NULL DEFAULT FALSE, -- 是否弹窗显示
    status SMALLINT NOT NULL DEFAULT 1, -- 状态：0-禁用，1-启用
    start_time TIMESTAMP NULL, -- 开始时间
    end_time TIMESTAMP NULL, -- 结束时间
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
-- 用户表索引
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);

-- 第三方账号表索引
CREATE INDEX idx_third_party_accounts_user_id ON third_party_accounts(user_id);
CREATE INDEX idx_third_party_accounts_platform ON third_party_accounts(platform);
CREATE INDEX idx_third_party_accounts_openid ON third_party_accounts(openid);
CREATE INDEX idx_third_party_accounts_unionid ON third_party_accounts(unionid);
CREATE UNIQUE INDEX idx_third_party_accounts_platform_openid ON third_party_accounts(platform, openid);

-- 竞猜记录表索引
CREATE INDEX idx_guess_records_user_id ON guess_records(user_id);
CREATE INDEX idx_guess_records_lottery_date ON guess_records(lottery_date);
CREATE INDEX idx_guess_records_status ON guess_records(status);
CREATE INDEX idx_guess_records_is_winner ON guess_records(is_winner);
CREATE INDEX idx_guess_records_created_at ON guess_records(created_at);

-- 现金记录表索引
CREATE INDEX idx_cash_records_user_id ON cash_records(user_id);
CREATE INDEX idx_cash_records_type ON cash_records(type);
CREATE INDEX idx_cash_records_created_at ON cash_records(created_at);

-- 资金流水记录表索引
CREATE INDEX idx_fund_flow_records_user_id ON fund_flow_records(user_id);
CREATE INDEX idx_fund_flow_records_flow_no ON fund_flow_records(flow_no);
CREATE INDEX idx_fund_flow_records_type ON fund_flow_records(type);
CREATE INDEX idx_fund_flow_records_source_type ON fund_flow_records(source_type);
CREATE INDEX idx_fund_flow_records_source_id ON fund_flow_records(source_id);
CREATE INDEX idx_fund_flow_records_status ON fund_flow_records(status);
CREATE INDEX idx_fund_flow_records_created_at ON fund_flow_records(created_at);

-- 提现记录表索引
CREATE INDEX idx_withdrawal_records_user_id ON withdrawal_records(user_id);
CREATE INDEX idx_withdrawal_records_status ON withdrawal_records(status);
CREATE INDEX idx_withdrawal_records_created_at ON withdrawal_records(created_at);

-- 邀请记录表索引
CREATE INDEX idx_invitation_records_inviter_id ON invitation_records(inviter_id);
CREATE INDEX idx_invitation_records_invitee_id ON invitation_records(invitee_id);
CREATE INDEX idx_invitation_records_level ON invitation_records(level);
CREATE UNIQUE INDEX idx_invitation_records_inviter_invitee ON invitation_records(inviter_id, invitee_id);

-- 开奖结果表索引
CREATE INDEX idx_lottery_results_status ON lottery_results(status);

-- 公告表索引
CREATE INDEX idx_announcements_type ON announcements(type);
CREATE INDEX idx_announcements_status ON announcements(status);
CREATE INDEX idx_announcements_start_time ON announcements(start_time);
CREATE INDEX idx_announcements_end_time ON announcements(end_time);

-- 插入初始系统配置数据
INSERT INTO system_configs (config_key, config_value, config_desc, config_type, is_public) VALUES
('min_withdrawal_amount', '10', '最小提现金额（元）', 'int', true),
('max_withdrawal_amount', '1000', '最大提现金额（元）', 'int', true),
('daily_guess_limit', '10', '每日竞猜次数限制', 'int', true),
('invitation_reward_level1', '10', '一级邀请奖励现金（元）', 'int', false),
('invitation_reward_level2', '5', '二级邀请奖励现金（元）', 'int', false),
('lottery_time', '12:00:00', '每日开奖时间', 'string', true),
('guess_deadline', '11:59:59', '竞猜截止时间', 'string', true),
('new_user_bonus', '100', '新用户注册奖励现金（元）', 'int', false),
('app_name', '有奖竞猜', '应用名称', 'string', true),
('app_version', '1.0.0', '应用版本', 'string', true);

-- 插入初始竞猜规则数据
INSERT INTO guess_rules (name, bet_amount, reward_amount, is_active, sort_order) VALUES
('标准竞猜', 10.00, 100.00, true, 1),
('高级竞猜', 50.00, 600.00, true, 2),
('豪华竞猜', 100.00, 1500.00, true, 3);
