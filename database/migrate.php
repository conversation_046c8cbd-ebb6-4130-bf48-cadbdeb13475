<?php
/**
 * 数据库迁移脚本
 * 用于创建微信小程序有奖竞猜项目的数据库表
 */

require_once __DIR__ . '/../vendor/autoload.php';

use support\Db;

try {
    echo "开始执行数据库迁移...\n";
    
    // 读取SQL文件
    $sqlFile = __DIR__ . '/migrations/create_tables.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: {$sqlFile}");
    }
    
    $sql = file_get_contents($sqlFile);
    if (empty($sql)) {
        throw new Exception("SQL文件内容为空");
    }
    
    // 分割SQL语句（以分号分割）
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($statement) {
            return !empty($statement) && !preg_match('/^\s*--/', $statement);
        }
    );
    
    echo "共找到 " . count($statements) . " 条SQL语句\n";
    
    // 执行SQL语句
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $index => $statement) {
        try {
            if (trim($statement)) {
                Db::execute($statement);
                $successCount++;
                echo "✓ 执行成功: 语句 " . ($index + 1) . "\n";
            }
        } catch (Exception $e) {
            $errorCount++;
            echo "✗ 执行失败: 语句 " . ($index + 1) . " - " . $e->getMessage() . "\n";
            // 继续执行其他语句，不中断
        }
    }
    
    echo "\n迁移完成!\n";
    echo "成功: {$successCount} 条\n";
    echo "失败: {$errorCount} 条\n";
    
    if ($errorCount > 0) {
        echo "\n注意: 有部分语句执行失败，请检查数据库连接和权限设置\n";
    }
    
} catch (Exception $e) {
    echo "迁移失败: " . $e->getMessage() . "\n";
    exit(1);
}
