# 微信小程序有奖竞猜数据库设计

## 项目概述

本项目是一个微信小程序有奖竞猜的后端API系统，用户通过授权登录后输入四位尾数发起有奖竞猜。竞猜开奖结果为某一时刻的上证指数、创业板指、深证成指精确到保留2位小数的最后四位。每天中午12点整开奖，猜对的获得指定数量的现金奖励，可直接提现到微信余额。

## 技术栈

- **后端框架**: Webman
- **数据库ORM**: think-orm
- **数据库**: PostgreSQL

## 数据库表结构

### 1. 用户表 (users)
存储用户基本信息，用户ID从100000开始自增
- `id`: 主键 (从100000开始)
- `nickname`: 昵称
- `avatar`: 头像
- `phone`: 手机号
- `email`: 邮箱
- `gender`: 性别 (0-未知，1-男，2-女)
- `birthday`: 生日
- `status`: 状态 (0-禁用，1-正常)
- `last_login_time`: 最后登录时间
- `last_login_ip`: 最后登录IP
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 2. 第三方账号表 (third_party_accounts)
存储微信等第三方授权信息，支持后期扩展
- `id`: 主键
- `user_id`: 用户ID (外键)
- `platform`: 平台 (wechat-微信)
- `platform_user_id`: 第三方平台用户ID
- `openid`: 微信openid
- `unionid`: 微信unionid
- `session_key`: 微信session_key
- `access_token`: 访问令牌
- `refresh_token`: 刷新令牌
- `expires_in`: 过期时间
- `extra_data`: 额外数据 (JSON格式)
- `status`: 状态
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 3. 用户资金表 (user_balances)
存储用户现金余额信息
- `id`: 主键
- `user_id`: 用户ID (外键，唯一)
- `cash_balance`: 现金余额（元）
- `frozen_cash`: 冻结现金（元）
- `total_earned`: 累计获得现金（元）
- `total_spent`: 累计消费现金（元）
- `total_withdrawn`: 累计提现金额（元）
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 4. 开奖结果表 (lottery_results)
存储每日开奖结果
- `id`: 主键
- `lottery_date`: 开奖日期 (唯一)
- `shanghai_index`: 上证指数
- `shenzhen_index`: 深证成指
- `chinext_index`: 创业板指
- `winning_number`: 中奖号码 (四位尾数)
- `status`: 状态 (0-未开奖，1-已开奖)
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 5. 竞猜记录表 (guess_records)
存储用户竞猜记录
- `id`: 主键
- `user_id`: 用户ID (外键)
- `lottery_date`: 竞猜期数 (日期)
- `guess_number`: 竞猜号码 (四位数)
- `bet_amount`: 投注现金数量（元）
- `is_winner`: 是否中奖
- `reward_amount`: 奖励现金数量（元）
- `status`: 状态 (0-待开奖，1-已开奖)
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 6. 现金记录表 (cash_records)
存储现金变动记录
- `id`: 主键
- `user_id`: 用户ID (外键)
- `type`: 类型 (1-竞猜投注，2-中奖奖励，3-邀请奖励，4-提现扣除，5-系统赠送，6-系统扣除)
- `amount`: 现金数量 (正数为增加，负数为减少)
- `balance_before`: 变动前余额
- `balance_after`: 变动后余额
- `related_id`: 关联ID (如竞猜记录ID、邀请记录ID等)
- `remark`: 备注
- `created_at`: 创建时间

### 7. 提现记录表 (withdrawal_records)
存储提现记录
- `id`: 主键
- `user_id`: 用户ID (外键)
- `amount`: 提现金额（元）
- `status`: 状态 (0-申请中，1-处理中，2-已完成，3-已拒绝)
- `platform`: 提现平台 (wechat)
- `platform_account`: 提现账号
- `transaction_id`: 交易流水号
- `reject_reason`: 拒绝原因
- `processed_at`: 处理时间
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 8. 邀请记录表 (invitation_records)
存储邀请关系，支持两层邀请关系
- `id`: 主键
- `inviter_id`: 邀请人ID (外键)
- `invitee_id`: 被邀请人ID (外键)
- `level`: 邀请层级 (1-直接邀请，2-间接邀请)
- `reward_amount`: 邀请奖励现金（元）
- `status`: 状态 (0-无效，1-有效)
- `created_at`: 创建时间

### 9. 竞猜规则表 (guess_rules)
存储竞猜规则配置
- `id`: 主键
- `name`: 规则名称
- `bet_amount`: 投注现金数量（元）
- `reward_amount`: 中奖奖励现金数量（元）
- `is_active`: 是否启用
- `sort_order`: 排序
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 10. 系统配置表 (system_configs)
存储系统配置参数
- `id`: 主键
- `config_key`: 配置键 (唯一)
- `config_value`: 配置值
- `config_desc`: 配置描述
- `config_type`: 配置类型 (string,int,float,bool,json)
- `is_public`: 是否公开 (前端可获取)
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 11. 资金流水记录表 (fund_flow_records)
存储详细的资金变动流水，用于审计和对账
- `id`: 主键
- `user_id`: 用户ID (外键)
- `flow_no`: 流水号 (唯一)
- `type`: 类型 (1-竞猜投注，2-中奖奖励，3-邀请奖励，4-提现扣除，5-系统赠送，6-系统扣除，7-充值，8-退款)
- `sub_type`: 子类型 (具体业务类型)
- `amount`: 变动金额 (正数为收入，负数为支出)
- `balance_before`: 变动前余额
- `balance_after`: 变动后余额
- `source_type`: 来源类型 (guess_win,invitation,system_gift,withdrawal等)
- `source_id`: 来源记录ID
- `channel`: 渠道 (system,wechat,admin等)
- `operator_id`: 操作员ID (管理员操作时)
- `client_ip`: 客户端IP
- `user_agent`: 用户代理
- `device_info`: 设备信息 (JSON)
- `extra_data`: 扩展数据 (JSON)
- `remark`: 备注
- `status`: 状态 (0-失败，1-成功，2-处理中)
- `created_at`: 创建时间

### 12. 公告表 (announcements)
存储系统公告
- `id`: 主键
- `title`: 公告标题
- `content`: 公告内容
- `type`: 类型 (1-系统公告，2-活动公告，3-维护公告)
- `is_popup`: 是否弹窗显示
- `status`: 状态 (0-禁用，1-启用)
- `start_time`: 开始时间
- `end_time`: 结束时间
- `created_at`: 创建时间
- `updated_at`: 更新时间

## 数据库索引

为了提高查询性能，已为以下字段创建索引：
- 用户表：phone, status, created_at
- 第三方账号表：user_id, platform, openid, unionid, (platform, openid)联合唯一索引
- 竞猜记录表：user_id, lottery_date, status, is_winner, created_at
- 现金记录表：user_id, type, created_at
- 资金流水记录表：user_id, flow_no, type, source_type, source_id, status, created_at
- 提现记录表：user_id, status, created_at
- 邀请记录表：inviter_id, invitee_id, level, (inviter_id, invitee_id)联合唯一索引
- 开奖结果表：status
- 公告表：type, status, start_time, end_time

## 初始数据

系统已预置以下配置数据：
- 最小提现金额：10元
- 最大提现金额：1000元
- 每日竞猜次数限制：10次
- 一级邀请奖励：10元
- 二级邀请奖励：5元
- 每日开奖时间：12:00:00
- 竞猜截止时间：11:59:59
- 新用户注册奖励：100元

竞猜规则预置：
- 标准竞猜：投注10元，中奖奖励100元
- 高级竞猜：投注50元，中奖奖励600元
- 豪华竞猜：投注100元，中奖奖励1500元

## 安装说明

1. 确保PostgreSQL数据库已安装并运行
2. 配置数据库连接信息（环境变量或config/think-orm.php）
3. 运行迁移脚本：
   ```bash
   php database/migrate.php
   ```

## 环境变量配置

在项目根目录创建 `.env` 文件，配置以下数据库连接信息：
```
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=minisoft_api
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_CHARSET=utf8
```

## 模型文件

已为每个表创建对应的模型文件，位于 `app/model/` 目录下：
- User.php - 用户模型
- ThirdPartyAccount.php - 第三方账号模型
- UserBalance.php - 用户余额模型
- GuessRecord.php - 竞猜记录模型
- CoinRecord.php - 金币记录模型
- WithdrawalRecord.php - 提现记录模型
- InvitationRecord.php - 邀请记录模型
- LotteryResult.php - 开奖结果模型
- SystemConfig.php - 系统配置模型
- GuessRule.php - 竞猜规则模型
- Announcement.php - 公告模型

每个模型都包含了相应的关联关系、常量定义和常用方法。
