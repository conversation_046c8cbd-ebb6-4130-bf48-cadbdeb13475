<?php

namespace app\middleware;

use app\model\Users;
use ReflectionClass;
use Webman\Http\Request;
use Webman\Http\Response;
use Webman\MiddlewareInterface;

class AdminAuth implements MiddlewareInterface
{
    public function process(Request $request, callable $handler): Response
    {
        try {

            // 检查是否已登录
            $user = getUserByToken();
            if ($user) {
                if (!in_array($user->role, ['admin', 'editor'])) {
                    return json(['code' => 403, 'msg' => '权限不足']);
                }
                if ($user->status!==1){
                    return json(['code' => 403, 'msg' => '账号已禁用']);
                }
                $request->user = $user;

                return $handler($request);
            }

            $controller = new ReflectionClass($request->controller);
            $noNeedLogin = $controller->getDefaultProperties()['NotNeedLogin'] ?? [];
            // 访问的方法需要登录
            if (in_array($request->action, $noNeedLogin)) {
                // 拦截请求，返回一个重定向响应，请求停止向洋葱芯穿越
                return $handler($request);
            }
            return  json(['code' => 401, 'msg' => '请先登录']);

        } catch (\ReflectionException|\Exception $e) {
            return json(['code' => 500, 'msg' => '请求异常: ' . $e->getMessage()]);
        }
    }
}