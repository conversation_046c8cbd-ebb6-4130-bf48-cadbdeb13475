<?php

namespace app\middleware;

use app\model\Users;
use ReflectionClass;
use Webman\Http\Request;
use Webman\Http\Response;
use Webman\MiddlewareInterface;

class WebAuth implements MiddlewareInterface
{
    public function process(Request $request, callable $handler): Response
    {

        try {
            $controller = new ReflectionClass($request->controller);
            $NeedLogin = $controller->getDefaultProperties()['NeedLogin'] ?? [];
            // 访问的方法需要登录
            if (in_array($request->action, $NeedLogin)) {
                $user = getUserByToken();
                if ($user) {
                    if ($user->status!==1){
                        return json(['code' => 403, 'msg' => '账号已禁用']);
                    }
                    $request->user = $user;
                }else{
                    return  json(['code' => 401, 'msg' => '请先登录']);
                }
            }
            return $handler($request);

        } catch (\ReflectionException|\Exception $e) {

            return json(['code' => 500, 'msg' => '请求异常: ' . $e->getMessage()]);
        }

    }
}