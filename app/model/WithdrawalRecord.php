<?php

namespace app\model;

use support\think\Model;

/**
 * 提现记录模型
 */
class WithdrawalRecord extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'pgsql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'withdrawal_records';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'amount', 'status', 'platform', 'platform_account',
        'transaction_id', 'reject_reason'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'amount' => 'decimal:2',
        'status' => 'integer',
        'processed_at' => 'datetime',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 状态常量
     */
    const STATUS_PENDING = 0;    // 申请中
    const STATUS_PROCESSING = 1; // 处理中
    const STATUS_COMPLETED = 2;  // 已完成
    const STATUS_REJECTED = 3;   // 已拒绝

    /**
     * 平台常量
     */
    const PLATFORM_WECHAT = 'wechat';

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            self::STATUS_PENDING => '申请中',
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_REJECTED => '已拒绝',
        ];
        
        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 获取平台文本
     *
     * @return string
     */
    public function getPlatformTextAttribute()
    {
        $platformMap = [
            self::PLATFORM_WECHAT => '微信',
        ];
        
        return $platformMap[$this->platform] ?? '未知';
    }

    /**
     * 审核通过
     *
     * @param string $transactionId
     * @return bool
     */
    public function approve($transactionId = '')
    {
        $this->status = self::STATUS_COMPLETED;
        $this->transaction_id = $transactionId;
        $this->processed_at = now();
        return $this->save();
    }

    /**
     * 审核拒绝
     *
     * @param string $reason
     * @return bool
     */
    public function reject($reason)
    {
        $this->status = self::STATUS_REJECTED;
        $this->reject_reason = $reason;
        $this->processed_at = now();
        return $this->save();
    }
}
