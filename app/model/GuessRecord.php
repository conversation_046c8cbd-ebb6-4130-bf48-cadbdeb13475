<?php

namespace app\model;

use support\think\Model;

/**
 * 竞猜记录模型
 */
class GuessRecord extends Model
{

    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'guess_records';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'lottery_date', 'guess_number', 'bet_amount',
        'is_winner', 'reward_amount', 'status'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'lottery_date' => 'date',
        'bet_amount' => 'decimal:2',
        'is_winner' => 'boolean',
        'reward_amount' => 'decimal:2',
        'status' => 'integer',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联开奖结果
     */
    public function lotteryResult()
    {
        return $this->belongsTo(LotteryResult::class, 'lottery_date', 'lottery_date');
    }

    /**
     * 状态常量
     */
    const STATUS_PENDING = 0;  // 待开奖
    const STATUS_FINISHED = 1; // 已开奖

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            self::STATUS_PENDING => '待开奖',
            self::STATUS_FINISHED => '已开奖',
        ];
        
        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 获取中奖状态文本
     *
     * @return string
     */
    public function getWinnerTextAttribute()
    {
        return $this->is_winner ? '中奖' : '未中奖';
    }
}
