<?php

namespace app\model;

use support\think\Model;

/**
 * 竞猜规则模型
 */
class GuessRule extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'pgsql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'guess_rules';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name', 'bet_amount', 'reward_amount', 'is_active', 'sort_order'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'bet_amount' => 'decimal:2',
        'reward_amount' => 'decimal:2',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * 获取启用的规则
     *
     * @return \think\Collection
     */
    public static function getActiveRules()
    {
        return self::where('is_active', true)
                  ->order('sort_order', 'asc')
                  ->select();
    }

    /**
     * 根据投注现金金额获取规则
     *
     * @param float $betAmount
     * @return static|null
     */
    public static function getRuleByBetAmount($betAmount)
    {
        return self::where('bet_amount', $betAmount)
                  ->where('is_active', true)
                  ->first();
    }

    /**
     * 获取赔率
     *
     * @return float
     */
    public function getOddsAttribute()
    {
        return $this->bet_amount > 0 ? round($this->reward_amount / $this->bet_amount, 2) : 0;
    }

    /**
     * 启用规则
     *
     * @return bool
     */
    public function enable()
    {
        $this->is_active = true;
        return $this->save();
    }

    /**
     * 禁用规则
     *
     * @return bool
     */
    public function disable()
    {
        $this->is_active = false;
        return $this->save();
    }
}
