<?php

namespace app\model;

use support\think\Model;

/**
 * 邀请记录模型
 */
class InvitationRecord extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'pgsql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'invitation_records';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'inviter_id', 'invitee_id', 'level', 'reward_amount', 'status'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'inviter_id' => 'integer',
        'invitee_id' => 'integer',
        'level' => 'integer',
        'reward_amount' => 'decimal:2',
        'status' => 'integer',
        'created_at' => 'datetime',
    ];

    /**
     * 关联邀请人
     */
    public function inviter()
    {
        return $this->belongsTo(User::class, 'inviter_id');
    }

    /**
     * 关联被邀请人
     */
    public function invitee()
    {
        return $this->belongsTo(User::class, 'invitee_id');
    }

    /**
     * 层级常量
     */
    const LEVEL_DIRECT = 1;   // 直接邀请
    const LEVEL_INDIRECT = 2; // 间接邀请

    /**
     * 状态常量
     */
    const STATUS_INVALID = 0; // 无效
    const STATUS_VALID = 1;   // 有效

    /**
     * 获取层级文本
     *
     * @return string
     */
    public function getLevelTextAttribute()
    {
        $levelMap = [
            self::LEVEL_DIRECT => '直接邀请',
            self::LEVEL_INDIRECT => '间接邀请',
        ];
        
        return $levelMap[$this->level] ?? '未知';
    }

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            self::STATUS_INVALID => '无效',
            self::STATUS_VALID => '有效',
        ];
        
        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 创建邀请记录
     *
     * @param int $inviterId
     * @param int $inviteeId
     * @param int $level
     * @param float $rewardAmount 奖励现金金额
     * @return static|false
     */
    public static function createInvitation($inviterId, $inviteeId, $level = self::LEVEL_DIRECT, $rewardAmount = 0)
    {
        // 检查是否已存在邀请关系
        $exists = self::where('inviter_id', $inviterId)
                     ->where('invitee_id', $inviteeId)
                     ->exists();
        
        if ($exists) {
            return false;
        }

        return self::create([
            'inviter_id' => $inviterId,
            'invitee_id' => $inviteeId,
            'level' => $level,
            'reward_amount' => $rewardAmount,
            'status' => self::STATUS_VALID,
        ]);
    }
}
