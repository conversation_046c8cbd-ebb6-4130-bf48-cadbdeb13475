<?php

namespace app\model;

use support\think\Model;

/**
 * 现金记录模型
 */
class CashRecord extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'pgsql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'cash_records';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'type', 'amount', 'balance_before', 'balance_after', 
        'related_id', 'remark'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'type' => 'integer',
        'amount' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
        'related_id' => 'integer',
        'created_at' => 'datetime',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 类型常量
     */
    const TYPE_GUESS_BET = 1;        // 竞猜投注
    const TYPE_GUESS_REWARD = 2;     // 中奖奖励
    const TYPE_INVITATION_REWARD = 3; // 邀请奖励
    const TYPE_WITHDRAWAL = 4;       // 提现扣除
    const TYPE_SYSTEM_GIFT = 5;      // 系统赠送
    const TYPE_SYSTEM_DEDUCT = 6;    // 系统扣除

    /**
     * 获取类型文本
     *
     * @return string
     */
    public function getTypeTextAttribute()
    {
        $typeMap = [
            self::TYPE_GUESS_BET => '竞猜投注',
            self::TYPE_GUESS_REWARD => '中奖奖励',
            self::TYPE_INVITATION_REWARD => '邀请奖励',
            self::TYPE_WITHDRAWAL => '提现扣除',
            self::TYPE_SYSTEM_GIFT => '系统赠送',
            self::TYPE_SYSTEM_DEDUCT => '系统扣除',
        ];

        return $typeMap[$this->type] ?? '未知';
    }

    /**
     * 创建现金记录
     *
     * @param int $userId
     * @param int $type
     * @param float $amount
     * @param float $balanceBefore
     * @param float $balanceAfter
     * @param int|null $relatedId
     * @param string $remark
     * @return static
     */
    public static function createRecord($userId, $type, $amount, $balanceBefore, $balanceAfter, $relatedId = null, $remark = '')
    {
        return self::create([
            'user_id' => $userId,
            'type' => $type,
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'related_id' => $relatedId,
            'remark' => $remark,
        ]);
    }
}
