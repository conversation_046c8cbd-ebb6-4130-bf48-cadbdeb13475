<?php

namespace app\model;

use support\think\Model;

/**
 * 资金流水记录模型
 */
class FundFlowRecord extends Model
{

    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'fund_flow_records';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'flow_no', 'type', 'sub_type', 'amount', 'balance_before', 
        'balance_after', 'source_type', 'source_id', 'channel', 'operator_id',
        'client_ip', 'user_agent', 'device_info', 'extra_data', 'remark', 'status'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'type' => 'integer',
        'sub_type' => 'integer',
        'amount' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
        'source_id' => 'integer',
        'operator_id' => 'integer',
        'device_info' => 'array',
        'extra_data' => 'array',
        'status' => 'integer',
        'created_at' => 'datetime',
    ];

    /**
     * 类型常量
     */
    const TYPE_GUESS_BET = 1;        // 竞猜投注
    const TYPE_GUESS_REWARD = 2;     // 中奖奖励
    const TYPE_INVITATION_REWARD = 3; // 邀请奖励
    const TYPE_WITHDRAWAL = 4;       // 提现扣除
    const TYPE_SYSTEM_GIFT = 5;      // 系统赠送
    const TYPE_SYSTEM_DEDUCT = 6;    // 系统扣除
    const TYPE_RECHARGE = 7;         // 充值
    const TYPE_REFUND = 8;           // 退款

    /**
     * 状态常量
     */
    const STATUS_FAILED = 0;         // 失败
    const STATUS_SUCCESS = 1;        // 成功
    const STATUS_PROCESSING = 2;     // 处理中

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联操作员
     */
    public function operator()
    {
        return $this->belongsTo(User::class, 'operator_id');
    }

    /**
     * 获取类型文本
     *
     * @return string
     */
    public function getTypeTextAttribute()
    {
        $typeMap = [
            self::TYPE_GUESS_BET => '竞猜投注',
            self::TYPE_GUESS_REWARD => '中奖奖励',
            self::TYPE_INVITATION_REWARD => '邀请奖励',
            self::TYPE_WITHDRAWAL => '提现扣除',
            self::TYPE_SYSTEM_GIFT => '系统赠送',
            self::TYPE_SYSTEM_DEDUCT => '系统扣除',
            self::TYPE_RECHARGE => '充值',
            self::TYPE_REFUND => '退款',
        ];
        
        return $typeMap[$this->type] ?? '未知';
    }

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            self::STATUS_FAILED => '失败',
            self::STATUS_SUCCESS => '成功',
            self::STATUS_PROCESSING => '处理中',
        ];
        
        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 生成流水号
     *
     * @param int $userId
     * @param int $type
     * @return string
     */
    public static function generateFlowNo($userId, $type)
    {
        $prefix = 'FL';
        $timestamp = date('YmdHis');
        $userIdPart = str_pad($userId % 10000, 4, '0', STR_PAD_LEFT);
        $typePart = str_pad($type, 2, '0', STR_PAD_LEFT);
        $random = str_pad(mt_rand(0, 999), 3, '0', STR_PAD_LEFT);
        
        return $prefix . $timestamp . $userIdPart . $typePart . $random;
    }

    /**
     * 创建资金流水记录
     *
     * @param array $data
     * @return static|false
     */
    public static function createRecord($data)
    {
        // 生成流水号
        if (empty($data['flow_no'])) {
            $data['flow_no'] = self::generateFlowNo($data['user_id'], $data['type']);
        }

        // 设置默认值
        $data = array_merge([
            'sub_type' => 0,
            'source_type' => 'system',
            'channel' => 'system',
            'client_ip' => request()->getRealIp() ?? '',
            'user_agent' => request()->header('user-agent') ?? '',
            'remark' => '',
            'status' => self::STATUS_SUCCESS,
        ], $data);

        return self::create($data);
    }

    /**
     * 根据来源查询记录
     *
     * @param string $sourceType
     * @param int $sourceId
     * @return \think\Collection
     */
    public static function getBySource($sourceType, $sourceId)
    {
        return self::where('source_type', $sourceType)
                  ->where('source_id', $sourceId)
                  ->order('created_at', 'desc')
                  ->select();
    }

    /**
     * 获取用户资金流水
     *
     * @param int $userId
     * @param int $page
     * @param int $limit
     * @return array
     */
    public static function getUserFlowRecords($userId, $page = 1, $limit = 20)
    {
        $offset = ($page - 1) * $limit;
        
        $records = self::where('user_id', $userId)
                      ->order('created_at', 'desc')
                      ->limit($offset, $limit)
                      ->select();
                      
        $total = self::where('user_id', $userId)->count();
        
        return [
            'records' => $records,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
}
