<?php

namespace app\model;

use support\think\Model;

/**
 * 用户余额模型
 */
class UserBalance extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'pgsql';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_balances';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'cash_balance', 'frozen_cash', 'total_earned',
        'total_spent', 'total_withdrawn'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'cash_balance' => 'decimal:2',
        'frozen_cash' => 'decimal:2',
        'total_earned' => 'decimal:2',
        'total_spent' => 'decimal:2',
        'total_withdrawn' => 'decimal:2',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 增加现金余额
     *
     * @param float $amount
     * @return bool
     */
    public function addCash($amount)
    {
        $this->cash_balance += $amount;
        $this->total_earned += $amount;
        return $this->save();
    }

    /**
     * 减少现金余额
     *
     * @param float $amount
     * @return bool
     */
    public function reduceCash($amount)
    {
        if ($this->cash_balance < $amount) {
            return false;
        }

        $this->cash_balance -= $amount;
        $this->total_spent += $amount;
        return $this->save();
    }

    /**
     * 冻结现金
     *
     * @param float $amount
     * @return bool
     */
    public function freezeCash($amount)
    {
        if ($this->cash_balance < $amount) {
            return false;
        }

        $this->cash_balance -= $amount;
        $this->frozen_cash += $amount;
        return $this->save();
    }

    /**
     * 解冻现金
     *
     * @param float $amount
     * @return bool
     */
    public function unfreezeCash($amount)
    {
        if ($this->frozen_cash < $amount) {
            return false;
        }

        $this->frozen_cash -= $amount;
        $this->cash_balance += $amount;
        return $this->save();
    }
}
