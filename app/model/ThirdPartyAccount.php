<?php

namespace app\model;

use support\think\Model;

/**
 * 第三方账号模型
 */
class ThirdPartyAccount extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'pgsql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'third_party_accounts';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'platform', 'platform_user_id', 'openid', 'unionid', 
        'session_key', 'access_token', 'refresh_token', 'expires_in', 
        'extra_data', 'status'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'expires_in' => 'integer',
        'status' => 'integer',
        'extra_data' => 'array',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 平台常量
     */
    const PLATFORM_WECHAT = 'wechat';
    
    /**
     * 状态常量
     */
    const STATUS_DISABLED = 0;
    const STATUS_NORMAL = 1;
}
