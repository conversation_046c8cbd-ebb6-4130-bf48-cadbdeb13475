<?php

namespace app\model;

use support\think\Model;

/**
 * 开奖结果模型
 */
class LotteryResult extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'pgsql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'lottery_results';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'lottery_date', 'shanghai_index', 'shenzhen_index', 'chinext_index', 
        'winning_number', 'status'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'lottery_date' => 'date',
        'shanghai_index' => 'decimal:2',
        'shenzhen_index' => 'decimal:2',
        'chinext_index' => 'decimal:2',
        'status' => 'integer',
    ];

    /**
     * 关联竞猜记录
     */
    public function guessRecords()
    {
        return $this->hasMany(GuessRecord::class, 'lottery_date', 'lottery_date');
    }

    /**
     * 状态常量
     */
    const STATUS_PENDING = 0;  // 未开奖
    const STATUS_FINISHED = 1; // 已开奖

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            self::STATUS_PENDING => '未开奖',
            self::STATUS_FINISHED => '已开奖',
        ];
        
        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 生成中奖号码
     *
     * @return string
     */
    public function generateWinningNumber()
    {
        // 取三个指数的最后四位数字
        $shanghai = str_pad((int)($this->shanghai_index * 100) % 10000, 4, '0', STR_PAD_LEFT);
        $shenzhen = str_pad((int)($this->shenzhen_index * 100) % 10000, 4, '0', STR_PAD_LEFT);
        $chinext = str_pad((int)($this->chinext_index * 100) % 10000, 4, '0', STR_PAD_LEFT);
        
        // 可以根据业务规则选择其中一个作为中奖号码，这里选择上证指数
        $this->winning_number = $shanghai;
        
        return $this->winning_number;
    }

    /**
     * 开奖
     *
     * @return bool
     */
    public function drawLottery()
    {
        if ($this->status == self::STATUS_FINISHED) {
            return false;
        }

        $this->generateWinningNumber();
        $this->status = self::STATUS_FINISHED;
        
        return $this->save();
    }

    /**
     * 获取今日开奖结果
     *
     * @return static|null
     */
    public static function getTodayResult()
    {
        return self::where('lottery_date', date('Y-m-d'))->first();
    }

    /**
     * 创建今日开奖记录
     *
     * @param float $shanghaiIndex
     * @param float $shenzhenIndex
     * @param float $chinextIndex
     * @return static
     */
    public static function createTodayResult($shanghaiIndex, $shenzhenIndex, $chinextIndex)
    {
        return self::create([
            'lottery_date' => date('Y-m-d'),
            'shanghai_index' => $shanghaiIndex,
            'shenzhen_index' => $shenzhenIndex,
            'chinext_index' => $chinextIndex,
            'status' => self::STATUS_PENDING,
        ]);
    }
}
