<?php

namespace app\model;

use support\think\Model;

/**
 * 系统配置模型
 */
class SystemConfig extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'pgsql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'system_configs';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'config_key', 'config_value', 'config_desc', 'config_type', 'is_public'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'is_public' => 'boolean',
    ];

    /**
     * 配置类型常量
     */
    const TYPE_STRING = 'string';
    const TYPE_INT = 'int';
    const TYPE_FLOAT = 'float';
    const TYPE_BOOL = 'bool';
    const TYPE_JSON = 'json';

    /**
     * 获取配置值
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getValue($key, $default = null)
    {
        $config = self::where('config_key', $key)->first();
        
        if (!$config) {
            return $default;
        }

        return self::castValue($config->config_value, $config->config_type);
    }

    /**
     * 设置配置值
     *
     * @param string $key
     * @param mixed $value
     * @param string $type
     * @param string $desc
     * @param bool $isPublic
     * @return bool
     */
    public static function setValue($key, $value, $type = self::TYPE_STRING, $desc = '', $isPublic = false)
    {
        $config = self::where('config_key', $key)->first();
        
        $data = [
            'config_key' => $key,
            'config_value' => is_array($value) ? json_encode($value) : (string)$value,
            'config_type' => $type,
            'config_desc' => $desc,
            'is_public' => $isPublic,
        ];

        if ($config) {
            return $config->update($data);
        } else {
            return self::create($data) ? true : false;
        }
    }

    /**
     * 类型转换
     *
     * @param string $value
     * @param string $type
     * @return mixed
     */
    protected static function castValue($value, $type)
    {
        switch ($type) {
            case self::TYPE_INT:
                return (int)$value;
            case self::TYPE_FLOAT:
                return (float)$value;
            case self::TYPE_BOOL:
                return in_array(strtolower($value), ['true', '1', 'yes', 'on']);
            case self::TYPE_JSON:
                return json_decode($value, true);
            default:
                return $value;
        }
    }

    /**
     * 获取公开配置
     *
     * @return array
     */
    public static function getPublicConfigs()
    {
        $configs = self::where('is_public', true)->get();
        $result = [];
        
        foreach ($configs as $config) {
            $result[$config->config_key] = self::castValue($config->config_value, $config->config_type);
        }
        
        return $result;
    }

    /**
     * 批量获取配置
     *
     * @param array $keys
     * @return array
     */
    public static function getMultiple(array $keys)
    {
        $configs = self::whereIn('config_key', $keys)->get();
        $result = [];
        
        foreach ($configs as $config) {
            $result[$config->config_key] = self::castValue($config->config_value, $config->config_type);
        }
        
        return $result;
    }
}
