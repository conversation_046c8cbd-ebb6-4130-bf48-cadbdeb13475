<?php

namespace app\model;

use support\think\Model;

/**
 * 公告模型
 */
class Announcement extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'pgsql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'announcements';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'title', 'content', 'type', 'is_popup', 'status', 'start_time', 'end_time'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'type' => 'integer',
        'is_popup' => 'boolean',
        'status' => 'integer',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];

    /**
     * 类型常量
     */
    const TYPE_SYSTEM = 1;     // 系统公告
    const TYPE_ACTIVITY = 2;   // 活动公告
    const TYPE_MAINTENANCE = 3; // 维护公告

    /**
     * 状态常量
     */
    const STATUS_DISABLED = 0; // 禁用
    const STATUS_ENABLED = 1;  // 启用

    /**
     * 获取类型文本
     *
     * @return string
     */
    public function getTypeTextAttribute()
    {
        $typeMap = [
            self::TYPE_SYSTEM => '系统公告',
            self::TYPE_ACTIVITY => '活动公告',
            self::TYPE_MAINTENANCE => '维护公告',
        ];
        
        return $typeMap[$this->type] ?? '未知';
    }

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            self::STATUS_DISABLED => '禁用',
            self::STATUS_ENABLED => '启用',
        ];
        
        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 获取有效的公告
     *
     * @param int|null $type
     * @return \think\Collection
     */
    public static function getValidAnnouncements($type = null)
    {
        $query = self::where('status', self::STATUS_ENABLED)
                    ->where(function($query) {
                        $query->whereNull('start_time')
                              ->orWhere('start_time', '<=', date('Y-m-d H:i:s'));
                    })
                    ->where(function($query) {
                        $query->whereNull('end_time')
                              ->orWhere('end_time', '>=', date('Y-m-d H:i:s'));
                    });

        if ($type !== null) {
            $query->where('type', $type);
        }

        return $query->order('created_at', 'desc')->select();
    }

    /**
     * 获取弹窗公告
     *
     * @return \think\Collection
     */
    public static function getPopupAnnouncements()
    {
        return self::getValidAnnouncements()
                  ->where('is_popup', true);
    }

    /**
     * 检查是否在有效期内
     *
     * @return bool
     */
    public function isValid()
    {
        $now = date('Y-m-d H:i:s');
        
        if ($this->status != self::STATUS_ENABLED) {
            return false;
        }

        if ($this->start_time && $this->start_time > $now) {
            return false;
        }

        if ($this->end_time && $this->end_time < $now) {
            return false;
        }

        return true;
    }
}
