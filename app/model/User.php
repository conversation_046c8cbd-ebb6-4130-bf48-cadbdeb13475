<?php

namespace app\model;

use think\model;

/**
 * 用户模型
 */
class User extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
//    protected $connection = 'pgsql';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'users';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'nickname', 'avatar', 'phone', 'email', 'gender', 'birthday', 'status'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'gender' => 'integer',
        'status' => 'integer',
        'birthday' => 'date',
        'last_login_time' => 'datetime',
    ];

    /**
     * 关联第三方账号
     */
    public function thirdPartyAccounts()
    {
        return $this->hasMany(ThirdPartyAccount::class, 'user_id');
    }

    /**
     * 关联用户余额
     */
    public function balance()
    {
        return $this->hasOne(UserBalance::class, 'user_id');
    }

    /**
     * 关联竞猜记录
     */
    public function guessRecords()
    {
        return $this->hasMany(GuessRecord::class, 'user_id');
    }

    /**
     * 关联现金记录
     */
    public function cashRecords()
    {
        return $this->hasMany(CashRecord::class, 'user_id');
    }

    /**
     * 关联资金流水记录
     */
    public function fundFlowRecords()
    {
        return $this->hasMany(FundFlowRecord::class, 'user_id');
    }

    /**
     * 关联提现记录
     */
    public function withdrawalRecords()
    {
        return $this->hasMany(WithdrawalRecord::class, 'user_id');
    }

    /**
     * 关联邀请记录（作为邀请人）
     */
    public function invitations()
    {
        return $this->hasMany(InvitationRecord::class, 'inviter_id');
    }

    /**
     * 关联邀请记录（作为被邀请人）
     */
    public function invitedBy()
    {
        return $this->hasOne(InvitationRecord::class, 'invitee_id');
    }
}
