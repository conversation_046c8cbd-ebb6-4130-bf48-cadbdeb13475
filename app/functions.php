<?php
/**
 * Here is your custom functions.
 */
function sendSmsBid($mobile, $content)
{
    // 国央企工程商机会员：${BidName}，建设地点：${City}，截止日期： ${Time} ，详情登录：https://screen-data.youcaiyun.com/list/big_data_detail?id=${BidId}查看
    $templateCode = 'SMS_269195026';
    $params = [
        'BidName' => $content['bid_name'],
        'City' => $content['bid_city'],
        'Time' => $content['bid_end_time'],
        'BidId' => $content['bid_id']
    ];
    //AccessKey ID ：
    //LTAI5tB7Vexbavz91A7X553y
    //AccessKey Secret：
    //******************************
    $config = [
        'access_key' => 'LTAI5tB7Vexbavz91A7X553y',
        'access_secret' => '******************************',
        'sign_name' => '优采云',
    ];
    $sms = new Mrgoon\AliSms\AliSms();
    $response = $sms->sendSms($mobile, $templateCode, $params, $config);
    if ($response->Code == 'OK') {
        return ['status' => 1, 'msg' => '发送成功'];
    } else {
        return ['status' => 0, 'msg' => $response->Message];
    }
}

function getAPIInfo($url)
{
    try {
        $client = new \GuzzleHttp\Client();
        $response = $client->request('GET', $url);
        $data =  json_decode($response->getBody(), true);
        if(!empty($data['info'])){
            return $data['info'];
        }
    } catch (\GuzzleHttp\Exception\GuzzleException|\Exception $e) {

    }
    return [];
}

function getUserByToken()
{
    try {
        return \Tinywan\Jwt\JwtToken::getUser();
    } catch (\Exception $e) {
        return false;
    }
}


function datetime()
{
    return date('Y-m-d H:i:s');
}

function ms()
{
    return doubleval(number_format(microtime(true)*1000 ,2,".","") );
}