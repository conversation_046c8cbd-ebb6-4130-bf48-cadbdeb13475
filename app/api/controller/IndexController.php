<?php

namespace app\api\controller;

use app\libs\PanApi;
use support\Request;
use Workerman\Coroutine;
use Workerman\Coroutine\Parallel;
use Workerman\Timer;


class IndexController extends BaseController
{

    public $NotNeedLogin = ['getOpenData'];

    public function index()
    {
        $pand = new PanApi;
        $info = $pand->getData();
        return $this->success($info);
    }

    //获取我的今日竞猜
    public function getOpenData(Request $request)
    {


    }


    public function parallel()
    {

        //协程
        $info = [];
        try {
            Coroutine::create(function () use ($info) {
                try {
                    Timer::sleep(1.5);
                    dump($info);
                } catch (\Exception $e) {
                    dump($e->getMessage());
                }
            });
        } catch (\Exception $exception) {
            dump($exception->getMessage());
        }
//        多线程
//        $parallel = new Parallel();
//        for ($i = 1; $i < 10; $i++) {
//            $parallel->add(function () use ($i) {
//                // 业务开始
//                $rd = rand(1, 10);
//                var_dump(" task 【{$i}】 - 延迟 $rd 秒 :" . datetime());
//                sleep($rd);
//                var_dump(" task 【{$i}】 - 执行完成 : " . datetime());
//                return $i;
//                // 结束
//            });
//        }
//        $results = $parallel->wait();
        return json([

        ]);
    }

    public function view(Request $request)
    {
        return view('index/view', ['name' => 'webman']);
    }

    public function json(Request $request)
    {
        return json(['code' => 0, 'msg' => 'ok']);
    }

}
