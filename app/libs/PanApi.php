<?php

namespace app\libs;

class PanApi
{
    public static function getRequestId()
    {
        //requestId": "90e129a3-53fd-1fb2-446e-68636d923804",
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    public function getPingan()
    {
        $requestId = self::getRequestId();
        $post_data = <<<JSON
{
  "version": "2.0",
  "channel": "MobileH5",
  "requestId": "{$requestId}",
  "cltplt": "h5",
  "cltver": "1.0",
  "aid": "",
  "sid": "",
  "ouid": "",
  "source": "",
  "body[listRange]": "107",
  "body[codeList]": ["SZ399006","SZ399001","SH000001"]
}
JSON;
        $header = [
            "User-Agent" => "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
            "Connection" => "keep-alive",
            "Accept" => "application/json",
            "Accept-Encoding" => "gzip, deflate, br, zstd",
            "Content-Type" => "application/x-www-form-urlencoded",
            "referer" => "https://m.stock.pingan.com/static/quote/quote/optional.html",
            "cookie" => "WEBTRENDS_ID=w_0efaaccb-67fe-49d2-bb65-d4845d02ea77"
        ];

        $api = 'https://quote.stock.pingan.com/restapi/nodeserver/quote/quoteIndex?_=' . time();
        $result = $this->request($api, json_decode($post_data, true), $header, 'POST');
        $data = [
            "status" => false,
            'data' => []
        ];
        if (($result['status'] ?? 0)) {
            $data['status'] = true;
            foreach ($result['results']['subBeans'] as $item) {
                $price = $item['newPrice'];
                $diff = $price - $item['prevClosePx'];
                $diff = number_format($diff, 2);
                $percent = number_format($diff / $item['prevClosePx'] * 100, 2);
                $data['data'][] = [
                    'name' => $item['name'],
                    'code' => $item['code'],
                    'price' => $price,
                    'diff' => $diff,
                    'percent' => $percent
                ];
            }
        }
        return $data;
    }

    public function getInfo()
    {
        $api = 'https://www.vk30.top/cgj/getinfo';
        $result = $this->request($api, []);
        if (!empty($result['info']['prices'])) {
            $prices = $result['info']['prices'];
            $prices[0] = ['name' => '上证指数', 'code' => 'SH000001'] + $prices[0]??[];
            $prices[1] = ['name' => '创业板指', 'code' => 'SZ399006'] + $prices[1]??[];
            $prices[2] = ['name' => '深证成指', 'code' => 'SZ399001'] + $prices[2]??[];
            return [
                'status' => true,
                'data' => $prices,
            ];
        }
        return ['status' => false, 'message' => '请求失败', 'data' => $result];
    }

    public function request($api, $data, $header = [], $method = 'GET')
    {
        try {
            $options = [
                'headers' => $header,
                'form_params' => $data
            ];
            $client = new \GuzzleHttp\Client();
            $response = $client->request($method, $api, $options);
            $content = $response->getBody()->getContents();
            $data = json_decode($content, true);
            if ($data) {
                return $data;
            }
            return ['status' => false, 'message' => '请求失败', 'data' => $content];
        } catch (\GuzzleHttp\Exception\GuzzleException|\Exception $e) {
            return ['status' => false, 'message' => $e->getMessage()];
        }
    }

}