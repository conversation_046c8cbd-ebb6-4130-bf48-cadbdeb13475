<?php

namespace app\command;

use app\libs\PanApi;
use app\model\Announcement;
use app\model\User;
use Mrgoon\AliSms\AliSms;
use Overtrue\Socialite\SocialiteManager;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Output\OutputInterface;
use think\db\Where;
use think\facade\Db;
use Workerman\Coroutine;
use Workerman\Coroutine\Parallel;
use Workerman\Timer;

#[AsCommand('test', 'test')]
class Test extends Command
{
    /**
     * @return void
     */
    protected function configure()
    {
        $this->addArgument('name', InputArgument::OPTIONAL, 'Name description');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $ms=0;
        $mod = 'pgsql';
        for ($i = 0; $i < 1000; $i++) {
//            User::find(100000);
            $m1 = ms();
            db('test_data',$mod)->order('id desc')->limit(30)->select();
            $ms = $ms + ( ms()-$m1);
            dump($ms);
        }

        dd($ms, db('test_data',$mod)->count());
        $socialite = new SocialiteManager([
            'qq' => [
                'client_id' => '102721381',
                'client_secret' => 'DG8mDRSHwOccNvIf',
                'redirect' => 'https://www.apptraces.com/login',
            ],
            'wechat' => [
                'provider' => 'wechat',
                'client_id' => 'wx97d00106de7c9995',
                'client_secret' => 'ffbac08845dd0668b1376b56f1cbb364',
                'redirect' => 'https://www.apptraces.com/login',
            ],

        ]);

        $user = $socialite->create('wechat')->userFromCode('12312323');

        dd($user);

//        $parallel = new Parallel();
//        for ($i=1; $i<5; $i++) {
//            $parallel->add(function () use ($i) {
//                // 业务开始
//                $rd = rand(1,5);
//                var_dump("$i - $rd");
//                sleep($rd);
//                return $i;
//                // 结束
//            });
//        }
//        $results = $parallel->wait();
//        dd($results);

//        Coroutine::create(function(){
//            Timer::sleep(1.5);
//            echo "hello coroutine\n";
//            dump(datetime());
//        });
        var_dump("xxxxx".strtotime(true));

//        var_dump(sendSmsBid('18875306618',[
//            "bid_name"=>"",
//            "bid_city"=>"bid_city",
//            "bid_end_time"=>"",
//            "bid_id"=>"",
//        ]));
//        exit();

//        'aliyun_sms'=>[
//        'access_key_id' => 'LTAI5t8GwFjcKanbNPfaKYR3',
//        'access_key_secret' => '******************************',
//        'TemplateCode'=>'SMS_317745001',//SMS_317745001   SMS_485025079
//        'sign_name' => '福建真创科技',//福建真创科技  看客帮
//
//    ]
//        $config = [
//            'access_key'=>'LTAI5t8GwFjcKanbNPfaKYR3',
//            'access_secret'=>'******************************',
//            'sign_name'=>'看客帮',
//        ];
//
//        $name = $input->getArgument('name');
//        $aliSms = new \Mrgoon\AliSms\AliSms();
//        $response = $aliSms->sendSms('15730487863', 'SMS_485025079', ['code'=> '1234'],$config);
//        var_dump($response);

        return self::SUCCESS;
    }

}
