<?php

namespace app\admin\controller;

use app\model\Categories;
use support\Request;
use support\Cache;

class BaseController
{
    protected $request;
    /**
     * @var array|mixed|null
     */
    protected mixed $user;

    public function __construct()
    {
        $this->request = \request();
//        $this->user = $this->request->user??null;
    }

    protected function view($template, $data = [])
    {
        $data['site_name'] = sys_config('site_name');
        $data['site_description'] = sys_config('site_description');
        $data['site_logo'] = sys_config('site_logo');
        $data['disclaimers'] = sys_config('disclaimers');
        $data['site_slogan'] = sys_config('site_slogan');

        $route_param = $this->request->route->param();

        $category_id = $route_param['category_id'] ?? 0;
        $parent_id = $route_param['parent_id'] ?? 0;

        $data['nav_list'] = Categories::getNavList($category_id, $parent_id);
        $template_dir = sys_config('site_template');

        $dir = $this->request->isMobile() ? "wap" : "pc";
        $data['template'] = $template_dir;
        $data['dir'] = "$template_dir/$dir";

        return view("$template_dir/$dir/$template", $data);
    }

    protected function success($msg = 'success', $data = [], $code = 200): \support\Response|\think\response\Json
    {
        if (is_array($msg) or is_object($msg)) {
            $data = $msg;
            $msg = 'success';
        }
        return json(['code' => $code, 'msg' => $msg, 'data' => $data]);
    }

    protected function error($msg = 'error', $data = [], $code = 500): \support\Response|\think\response\Json
    {
        if (is_array($msg) or is_object($msg)) {
            $data = $msg;
            $msg = 'error';
        }
        return json(['code' => $code, 'msg' => $msg, 'data' => $data]);
    }

}
