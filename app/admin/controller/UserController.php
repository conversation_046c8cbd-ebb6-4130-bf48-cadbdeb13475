<?php

namespace app\admin\controller;

use app\model\User;
use app\model\UserBalance;
use app\model\ThirdPartyAccount;
use support\Request;
use support\Db;

/**
 * 用户管理控制器
 */
class UserController extends BaseController
{
    /**
     * 用户列表
     *
     * @param Request $request
     * @return \support\Response
     */
    public function index(Request $request)
    {
        try {
            // 获取查询参数
            $params = $request->all();
            $page = (int)($params['page'] ?? 1);
            $limit = (int)($params['limit'] ?? 20);
            $nickname = $params['nickname'] ?? '';
            $user_id = $params['user_id'] ?? '';
            $status = $params['status'] ?? '';
            $start_time = $params['start_time'] ?? '';
            $end_time = $params['end_time'] ?? '';

            // 构建查询
            $query = User::with(['balance', 'thirdPartyAccounts']);

            // 昵称查询
            if (!empty($nickname)) {
                $query->where('nickname', 'like', '%' . $nickname . '%');
            }

            // 用户ID查询
            if (!empty($user_id)) {
                $query->where('id', $user_id);
            }

            // 用户状态查询
            if ($status !== '') {
                $query->where('status', $status);
            }

            // 注册时间范围查询
            if (!empty($start_time)) {
                $query->where('created_at', '>=', $start_time . ' 00:00:00');
            }
            if (!empty($end_time)) {
                $query->where('created_at', '<=', $end_time . ' 23:59:59');
            }

            // 按ID倒序排列
            $query->order('id', 'desc');

            // 分页查询
            $total = $query->count();
            $users = $query->page($page, $limit)->select();

            // 格式化数据
            $list = [];
            foreach ($users as $user) {
                $userData = $user->toArray();
                
                // 添加余额信息
                $userData['balance_info'] = $user->balance ? [
                    'cash_balance' => $user->balance->cash_balance,
                    'frozen_cash' => $user->balance->frozen_cash,
                    'total_earned' => $user->balance->total_earned,
                    'total_spent' => $user->balance->total_spent,
                    'total_withdrawn' => $user->balance->total_withdrawn,
                ] : [
                    'cash_balance' => '0.00',
                    'frozen_cash' => '0.00',
                    'total_earned' => '0.00',
                    'total_spent' => '0.00',
                    'total_withdrawn' => '0.00',
                ];

                // 添加第三方账号信息
                $userData['wechat_info'] = null;
                if ($user->thirdPartyAccounts) {
                    foreach ($user->thirdPartyAccounts as $account) {
                        if ($account->platform === 'wechat') {
                            $userData['wechat_info'] = [
                                'openid' => $account->openid,
                                'unionid' => $account->unionid,
                                'status' => $account->status,
                            ];
                            break;
                        }
                    }
                }

                // 添加状态和性别名称
                $userData['status_name'] = $user->status_name;
                $userData['gender_name'] = $user->gender_name;

                $list[] = $userData;
            }

            return $this->success([
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ]);

        } catch (\Exception $e) {
            return $this->error('获取用户列表失败：' . $e->getMessage());
        }
    }

    /**
     * 用户详情
     *
     * @param Request $request
     * @return \support\Response
     */
    public function show(Request $request)
    {
        try {
            $id = $request->get('id');
            if (empty($id)) {
                return $this->error('用户ID不能为空');
            }

            $user = User::with([
                'balance', 
                'thirdPartyAccounts', 
                'guessRecords' => function($query) {
                    $query->order('id', 'desc')->limit(10);
                },
                'withdrawalRecords' => function($query) {
                    $query->order('id', 'desc')->limit(10);
                },
                'invitations',
                'invitedBy'
            ])->find($id);

            if (!$user) {
                return $this->error('用户不存在');
            }

            $userData = $user->toArray();
            $userData['status_name'] = $user->status_name;
            $userData['gender_name'] = $user->gender_name;

            return $this->success($userData);

        } catch (\Exception $e) {
            return $this->error('获取用户详情失败：' . $e->getMessage());
        }
    }

    /**
     * 更新用户状态
     *
     * @param Request $request
     * @return \support\Response
     */
    public function updateStatus(Request $request)
    {
        try {
            $id = $request->post('id');
            $status = $request->post('status');

            if (empty($id)) {
                return $this->error('用户ID不能为空');
            }

            if (!in_array($status, [User::STATUS_DISABLED, User::STATUS_NORMAL])) {
                return $this->error('状态参数错误');
            }

            $user = User::find($id);
            if (!$user) {
                return $this->error('用户不存在');
            }

            $user->status = $status;
            $user->save();

            return $this->success('状态更新成功');

        } catch (\Exception $e) {
            return $this->error('更新状态失败：' . $e->getMessage());
        }
    }

    /**
     * 用户统计信息
     *
     * @param Request $request
     * @return \support\Response
     */
    public function statistics(Request $request)
    {
        try {
            // 总用户数
            $totalUsers = User::count();
            
            // 正常用户数
            $normalUsers = User::where('status', User::STATUS_NORMAL)->count();
            
            // 禁用用户数
            $disabledUsers = User::where('status', User::STATUS_DISABLED)->count();
            
            // 今日新增用户
            $todayUsers = User::whereDate('created_at', date('Y-m-d'))->count();
            
            // 本月新增用户
            $monthUsers = User::whereMonth('created_at', date('m'))
                             ->whereYear('created_at', date('Y'))
                             ->count();

            // 用户余额统计
            $balanceStats = UserBalance::selectRaw('
                SUM(cash_balance) as total_balance,
                SUM(frozen_cash) as total_frozen,
                SUM(total_earned) as total_earned,
                SUM(total_spent) as total_spent,
                SUM(total_withdrawn) as total_withdrawn
            ')->first();

            return $this->success([
                'user_stats' => [
                    'total_users' => $totalUsers,
                    'normal_users' => $normalUsers,
                    'disabled_users' => $disabledUsers,
                    'today_users' => $todayUsers,
                    'month_users' => $monthUsers,
                ],
                'balance_stats' => [
                    'total_balance' => $balanceStats->total_balance ?? '0.00',
                    'total_frozen' => $balanceStats->total_frozen ?? '0.00',
                    'total_earned' => $balanceStats->total_earned ?? '0.00',
                    'total_spent' => $balanceStats->total_spent ?? '0.00',
                    'total_withdrawn' => $balanceStats->total_withdrawn ?? '0.00',
                ]
            ]);

        } catch (\Exception $e) {
            return $this->error('获取统计信息失败：' . $e->getMessage());
        }
    }

    /**
     * 获取筛选选项
     *
     * @param Request $request
     * @return \support\Response
     */
    public function getFilterOptions(Request $request)
    {
        try {
            return $this->success([
                'status_options' => User::getStatusList(),
                'gender_options' => User::getGenderList(),
            ]);

        } catch (\Exception $e) {
            return $this->error('获取筛选选项失败：' . $e->getMessage());
        }
    }

    /**
     * 批量操作
     *
     * @param Request $request
     * @return \support\Response
     */
    public function batchAction(Request $request)
    {
        try {
            $action = $request->post('action');
            $ids = $request->post('ids', []);

            if (empty($action) || empty($ids)) {
                return $this->error('参数错误');
            }

            if (!is_array($ids)) {
                return $this->error('用户ID列表格式错误');
            }

            switch ($action) {
                case 'enable':
                    User::whereIn('id', $ids)->update(['status' => User::STATUS_NORMAL]);
                    return $this->success('批量启用成功');
                    
                case 'disable':
                    User::whereIn('id', $ids)->update(['status' => User::STATUS_DISABLED]);
                    return $this->success('批量禁用成功');
                    
                default:
                    return $this->error('不支持的操作');
            }

        } catch (\Exception $e) {
            return $this->error('批量操作失败：' . $e->getMessage());
        }
    }
}
